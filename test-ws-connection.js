const WebSocket = require('ws');

// Test WebSocket connection to trigger page creation
const ws = new WebSocket('ws://localhost:3033/collab');

ws.on('open', function open() {
    console.log('WebSocket connected');
    
    // Send a simple message to trigger document loading
    const message = JSON.stringify({
        type: 'sync',
        documentName: 'test-document-123'
    });
    
    console.log('Sending message:', message);
    ws.send(message);
});

ws.on('message', function message(data) {
    console.log('Received:', data.toString());
});

ws.on('error', function error(err) {
    console.error('WebSocket error:', err);
});

ws.on('close', function close(code, reason) {
    console.log('WebSocket closed:', code, reason.toString());
});

// Close after 5 seconds
setTimeout(() => {
    console.log('Closing connection...');
    ws.close();
}, 5000);
