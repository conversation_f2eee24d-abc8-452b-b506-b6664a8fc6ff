{"branches": ["main"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "angular", "parserOpts": {"noteKeywords": ["BREAKING CHANGE", "BREAKING CHANGES", "BREAKING"]}, "releaseRules": [{"type": "fix", "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "test", "release": "major"}, {"breaking": true, "release": "major"}, {"type": "feat", "scope": "api", "release": "minor"}, {"type": "feat", "scope": "ui", "release": "minor"}, {"type": "fix", "scope": "api", "release": "patch"}, {"type": "chore", "scope": "breaking", "release": "major"}, {"type": "refactor", "scope": "breaking", "release": "major"}, {"type": "refactor", "breaking": true, "release": "major"}, {"type": "feat", "breaking": true, "release": "major"}, {"type": "fix", "breaking": true, "release": "major"}, {"type": "docs", "breaking": true, "release": "major"}, {"type": "chore", "scope": "deps", "release": "patch"}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "parserOpts": {"noteKeywords": ["BREAKING CHANGE", "BREAKING CHANGES", "BREAKING"]}, "writerOpts": {"commitsSort": ["scope", "subject"], "types": [{"type": "feat", "section": "Новые функции"}, {"type": "fix", "section": "Исправления ошибок"}, {"type": "docs", "section": "Документация"}, {"type": "style", "section": "Стиль кода"}, {"type": "refactor", "section": "Рефактор<PERSON>нг"}, {"type": "test", "section": "Тесты"}, {"type": "chore", "section": "Обслуживание"}]}}], "@semantic-release/changelog", ["@semantic-release/npm", {"npmPublish": true, "tarballDir": "dist"}], ["@semantic-release/git", {"assets": ["package.json", "CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], ["@semantic-release/gitlab", {"gitlabUrl": "https://gitlab.sksp.site", "addReleaseNotes": true, "releaseNotes": true, "assets": [{"path": "dist/*.tgz", "label": "NPM Package", "type": "package"}]}]]}