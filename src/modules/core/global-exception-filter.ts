import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { CustomError, ERROR_CODE } from '@skillspace/utils';
import { Request, Response } from 'express';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
    private readonly logger = new Logger(GlobalExceptionFilter.name);

    catch(exception: any, host: ArgumentsHost) {
        const type = host.getType<'graphql' | 'http' | 'rmq' | 'rpc'>();

        // Обработка пользовательских ошибок (CustomError)
        if (exception instanceof CustomError) {
            const details = exception.details ?? {};
            this.logger.warn(
                {
                    code: exception.code ?? ERROR_CODE.INTERNAL_ERROR,
                    cause: exception.cause ?? null,
                    ...details,
                },
                exception.message,
            );
            return;
        }

        // Обработка HTTP-запросов
        if (type === 'http') {
            const ctx = host.switchToHttp();
            const response = ctx.getResponse<Response>();
            const request = ctx.getRequest<Request>();

            if (exception instanceof HttpException) {
                if (exception.getStatus() === HttpStatus.NOT_FOUND) {
                    this.logger.warn(
                        {
                            method: request.method,
                            url: request.url,
                        },
                        'Путь не найден: ' + request.url,
                    );

                    return response.status(HttpStatus.NOT_FOUND).json({
                        statusCode: HttpStatus.NOT_FOUND,
                        message: 'Путь не найден: ' + request.url,
                        timestamp: new Date().toISOString(),
                        path: request.url,
                    });
                }
            }
        }

        // Логирование всех остальных ошибок
        const error = exception instanceof Error ? exception : new Error('Internal server error');

        const errInfo = {
            error: {
                message: error.message || 'Неизвестная ошибка',
                cause: error.cause,
                stack: error.stack,
            },
        };

        this.logger.error(errInfo, 'Ошибка в работе сервиса: ' + error.message);
    }
}
