import {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Catch,
    ExceptionFilter,
    ExecutionContext,
    Global,
    HttpException,
    HttpStatus,
    Injectable,
    Logger,
    Module,
    NestInterceptor,
} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_INTERCEPTOR, Reflector } from '@nestjs/core';
import { CqrsModule } from '@nestjs/cqrs';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { AuthModule } from '@skillspace/access';
import {
    ApolloFederationDriver,
    ApolloFederationDriverConfig,
    ApolloServerPluginInlineTraceDisabled,
    GqlInterceptor,
    GraphqlExceptionFilter,
    GraphQLModule,
} from '@skillspace/graphql';
import { LoggerModule } from '@skillspace/logger';
import { OpentelemetryModule } from '@skillspace/tracing';
import { CustomError, ERROR_CODE } from '@skillspace/utils';
import { Request, Response } from 'express';
import { Observable } from 'rxjs';

import { appConfig, NODE_ENV } from '../../configs/app.config';
import { DatabaseModule } from '../../database/database.module';

@Injectable()
export class GlobalInterceptor implements NestInterceptor {
    constructor(private readonly gqlInterceptor: GqlInterceptor) {}

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const type = context.getType<'graphql' | 'rpc'>();
        if (type === 'graphql') {
            return this.gqlInterceptor.intercept(context, next);
        }
        return next.handle();
    }
}

@Module({
    providers: [
        GqlInterceptor,
        {
            provide: APP_INTERCEPTOR,
            useClass: GlobalInterceptor,
        },
    ],
})
export class GlobalInterceptorModule {}

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
    private readonly logger = new Logger(GlobalExceptionFilter.name);

    constructor() {}

    catch(exception: any, host: ArgumentsHost) {
        const type = host.getType<'graphql' | 'http' | 'rmq' | 'rpc'>();

        // Обработка пользовательских ошибок (CustomError)
        if (exception instanceof CustomError) {
            const details = exception.details ?? {};
            this.logger.warn(
                {
                    code: exception.code ?? ERROR_CODE.INTERNAL_ERROR,
                    cause: exception.cause ?? null,
                    ...details,
                },
                exception.message,
            );
            return;
        }

        // Обработка HTTP-запросов
        if (type === 'http') {
            const ctx = host.switchToHttp();
            const response = ctx.getResponse<Response>();
            const request = ctx.getRequest<Request>();

            if (exception instanceof HttpException) {
                if (exception.getStatus() === HttpStatus.NOT_FOUND) {
                    this.logger.warn(
                        {
                            method: request.method,
                            url: request.url,
                        },
                        'Путь не найден: ' + request.url,
                    );

                    return response.status(HttpStatus.NOT_FOUND).json({
                        statusCode: HttpStatus.NOT_FOUND,
                        message: 'Путь не найден: ' + request.url,
                        timestamp: new Date().toISOString(),
                        path: request.url,
                    });
                }
            }
        }

        // Логирование всех остальных ошибок
        const error = exception instanceof Error ? exception : new Error('Internal server error');

        const errInfo = {
            error: {
                message: error.message || 'Неизвестная ошибка',
                cause: error.cause,
                stack: error.stack,
            },
        };

        this.logger.error(errInfo, 'Ошибка в работе сервиса: ' + error.message);
    }
}

@Module({
    providers: [
        GraphqlExceptionFilter,
        {
            provide: APP_FILTER,
            useClass: GlobalExceptionFilter,
        },
    ],
})
export class GlobalExceptionModule {}

@Global()
@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
            load: [() => ({ app: appConfig })],
        }),
        EventEmitterModule.forRoot(),
        CqrsModule,
        ScheduleModule.forRoot(),
        AuthModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: async (configService: ConfigService) => ({
                global: true,
                secret: configService.get<string>('JWT_SECRET'),
            }),
        }),
        GraphQLModule.forRoot<ApolloFederationDriverConfig>({
            driver: ApolloFederationDriver,
            playground: appConfig.nodeEnv !== NODE_ENV.PROD,
            autoSchemaFile: true,
            formatError: (error) => {
                return {
                    message: error.message,
                    path: error.path,
                };
            },
            plugins: appConfig.nodeEnv === NODE_ENV.TEST ? [ApolloServerPluginInlineTraceDisabled()] : [],
        }),
        LoggerModule.forRoot(),
        // Only load OpenTelemetry if not disabled
        ...(process.env.OTEL_DISABLED !== 'true' ? [OpentelemetryModule.forRoot()] : []),
        GlobalExceptionModule,
        GlobalInterceptorModule,
        DatabaseModule,
    ],
    providers: [
        // Temporarily disable auth guards for testing
        // {
        //     provide: APP_GUARD,
        //     useClass: JwtAuthGqlGuard,
        // },
        // {
        //     provide: APP_GUARD,
        //     useClass: PermissionsGuard,
        // },
        Reflector,
    ],
    exports: [AuthModule],
})
export class CoreModule {}
