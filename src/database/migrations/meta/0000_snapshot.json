{"id": "c99c8261-1816-4d01-a56c-2921e0533a6e", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.pages": {"name": "pages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": false}, "text_content": {"name": "text_content", "type": "text", "primaryKey": false, "notNull": false}, "is_locked": {"name": "is_locked", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "service_id": {"name": "service_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "school_id": {"name": "school_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "ydoc": {"name": "ydoc", "type": "bytea", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}