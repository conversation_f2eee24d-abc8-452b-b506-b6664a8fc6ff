import * as process from 'node:process';
import { Global, Inject, Logger, Module, OnApplicationBootstrap } from '@nestjs/common';
import { sql } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool, types } from 'pg';

import { appConfig, isTest } from '../configs/app.config';
import { schema } from './schema';
import { DrizzleDB } from './types/drizzle.types';

// https://github.com/brianc/node-postgres/issues/811
types.setTypeParser(types.builtins.INT8, (val) => Number(val));

export const DATABASE_CONNECTION = 'DATABASE_CONNECTION';

@Global()
@Module({
    imports: [],
    providers: [
        {
            provide: DATABASE_CONNECTION,
            useFactory: () => {
                const pool = new Pool({
                    connectionString: appConfig.dbUrl,
                });

                pool.on('error', (err) => {
                    console.error('Database error:', err.message);
                });

                return drizzle(pool, {
                    schema,
                    logger: !isTest,
                });
            },
        },
    ],
    exports: [DATABASE_CONNECTION],
})
export class DatabaseModule implements OnApplicationBootstrap {
    private readonly logger = new Logger(DatabaseModule.name);

    constructor(@Inject(DATABASE_CONNECTION) private readonly db: DrizzleDB) {}

    async onApplicationBootstrap() {
        await this.establishConnection();
    }

    async establishConnection() {
        const retryAttempts = 15;
        const retryDelay = 3000;

        this.logger.log('Establishing database connection');
        for (let i = 0; i < retryAttempts; i++) {
            try {
                await this.db.execute(sql`SELECT 1=1`);
                this.logger.log('Database connection successful');
                break;
            } catch (err) {
                if (err.errors) {
                    this.logger.error(err.errors[0]);
                } else {
                    this.logger.error(err);
                }

                if (i < retryAttempts - 1) {
                    this.logger.log(`Retrying [${i + 1}/${retryAttempts}] in ${retryDelay / 1000} seconds`);
                    await new Promise((resolve) => setTimeout(resolve, retryDelay));
                } else {
                    this.logger.error(`Failed to connect to database after ${retryAttempts} attempts. Exiting...`);
                    process.exit(1);
                }
            }
        }
    }
}
